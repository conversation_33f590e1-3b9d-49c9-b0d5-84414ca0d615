# Full FastAPI backend optimized for LangGraph Migration
FROM python:3.10-slim AS builder

WORKDIR /app

# Install system dependencies for building packages
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Create and activate virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Install Python dependencies in chunks to avoid memory issues
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir wheel setuptools && \
    # Install core dependencies first
    pip install --no-cache-dir fastapi==0.115.12 uvicorn==0.34.2 pydantic[email]==2.11.4 python-dotenv==1.1.0 && \
    # Install additional validators
    pip install --no-cache-dir email-validator pydantic[email] && \
    # Install LangGraph and CopilotKit dependencies (specific versions for compatibility)
    pip install --no-cache-dir langgraph==0.2.50 copilotkit==0.1.46 && \
    # Install LangChain dependencies
    pip install --no-cache-dir langchain==0.3.25 langchain-core==0.3.60 langchain-openai==0.3.17 && \
    # Install scientific and data processing libraries
    pip install --no-cache-dir numpy>=1.24.0 pandas>=2.0.0 scipy>=1.10.0 && \
    # Install supabase and API dependencies
    pip install --no-cache-dir supabase>=2.3.0 python-multipart>=0.0.6 PyJWT>=2.9.0 && \
    # Install AI-related dependencies
    pip install --no-cache-dir openai>=1.3.7 && \
    # Install storage dependencies
    pip install --no-cache-dir google-cloud-storage>=2.14.0 pinecone>=5.1.0 && \
    # Install database dependencies
    pip install --no-cache-dir neo4j>=5.15.0 redis>=5.0.0 && \
    # Install embeddings-related packages
    pip install --no-cache-dir sentence-transformers>=2.2.2 voyageai>=0.1.6 && \
    # Install utility packages
    pip install --no-cache-dir backoff>=2.2.1 tqdm>=4.66.2 && \
    # Install monitoring and tracing packages
    pip install --no-cache-dir langsmith>=0.3.42

# Final, slimmer image
FROM python:3.10-slim

WORKDIR /app

# Install minimal runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PATH="/opt/venv/bin:$PATH"

# Create directories for application code and data
RUN mkdir -p /app/src
RUN mkdir -p /app/data

# Copy application code
COPY src/ /app/src/
COPY setup.py /app/
COPY minimal_server.py /app/
COPY simple_server.py /app/

# Create a non-root user to run the application
RUN groupadd -g 1001 appgroup && \
    useradd -m -u 1001 -g appgroup -s /bin/bash appuser && \
    chown -R appuser:appgroup /app && \
    chown -R appuser:appgroup /opt/venv

# Switch to non-root user for better security
USER appuser

# Install the package in development mode so imports work
RUN pip install -e .

EXPOSE 8000

# Health check to verify the application is running
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Command to run minimal server using virtual environment Python
CMD ["/opt/venv/bin/python", "minimal_server.py"]
