"""
Minimal FastAPI Server for PI Lawyer AI

This is a completely isolated minimal server that doesn't import any of the
complex dependencies that might cause startup crashes.
"""

import os
import sys
import logging
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("minimal_server")

logger.info("Starting minimal server...")

# Import FastAPI with error handling
try:
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    import uvicorn
    logger.info("FastAPI and uvicorn imported successfully")
except ImportError as e:
    logger.error(f"Failed to import FastAPI or uvicorn: {e}")
    sys.exit(1)

# Create a minimal FastAPI app
logger.info("Creating FastAPI application...")
app = FastAPI(
    title="PI Lawyer AI API",
    description="Minimal server for PI Lawyer AI",
    version="1.0.0"
)

# Add basic CORS middleware
logger.info("Adding CORS middleware...")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, this should be restricted
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger.info("FastAPI application created successfully")

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring and container health checks."""
    import platform
    import time

    # Get application start time (or use current time if not available)
    start_time = getattr(app, "start_time", time.time())
    uptime_seconds = int(time.time() - start_time)

    # Basic system information
    system_info = {
        "python_version": platform.python_version(),
        "system": platform.system(),
        "platform": platform.platform(),
    }

    return {
        "status": "ok",
        "version": "1.0.0",
        "environment": os.environ.get("APP_ENV", "development"),
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "uptime_seconds": uptime_seconds,
        "system_info": system_info,
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint that returns a simple message."""
    return {"message": "PI Lawyer AI API is running"}

def main():
    """Run the uvicorn server."""
    port = int(os.getenv("PORT", "8000"))
    logger.info(f"Starting uvicorn server on port {port}...")
    uvicorn.run(
        "minimal_server:app",
        host="0.0.0.0",
        port=port,
    )

if __name__ == "__main__":
    main()
