"""
Configuration management for the PI Lawyer AI system.

This module provides a centralized configuration system with enhanced validation,
type conversion, and configuration reporting. It loads environment variables from
.env files and provides default values for development and testing.
"""

import inspect
import logging
import os
import re
from enum import Enum
from functools import lru_cache
from typing import Any, Dict, List, Optional, Type, TypeVar, Union, cast, get_type_hints

from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("pi_lawyer.config")

# Load environment variables
if os.path.exists(".env"):
    load_dotenv(".env")
    logger.info("Loaded environment variables from .env")
elif os.path.exists(".env.test"):
    load_dotenv(".env.test")
    logger.info("Loaded environment variables from .env.test")
else:
    logger.warning("No .env or .env.test file found")

# Define environment types
T = TypeVar("T")


class LogLevel(str, Enum):
    """Valid log levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class JWTAlgorithm(str, Enum):
    """Valid JWT algorithms."""
    HS256 = "HS256"
    HS384 = "HS384"
    HS512 = "HS512"
    RS256 = "RS256"
    RS384 = "RS384"
    RS512 = "RS512"


class ValidationError(Exception):
    """Exception raised for configuration validation errors."""
    pass


class ConfigurationError(Exception):
    """Exception raised for configuration errors."""
    pass


def _parse_bool(val: Any) -> bool:
    """
    Parse a boolean value from various input types.
    
    Args:
        val: The value to parse
        
    Returns:
        bool: The parsed boolean value
        
    Raises:
        ValueError: If the value cannot be parsed as a boolean
    """
    if isinstance(val, bool):
        return val
    if isinstance(val, (int, float)):
        return bool(val)
    if isinstance(val, str):
        val = val.lower()
        if val in ("y", "yes", "t", "true", "on", "1"):
            return True
        if val in ("n", "no", "f", "false", "off", "0"):
            return False
    raise ValueError(f"Cannot parse {val} as a boolean")


def _parse_int(val: Any) -> int:
    """
    Parse an integer value from various input types.
    
    Args:
        val: The value to parse
        
    Returns:
        int: The parsed integer value
        
    Raises:
        ValueError: If the value cannot be parsed as an integer
    """
    if isinstance(val, int) and not isinstance(val, bool):
        return val
    if isinstance(val, str):
        try:
            return int(val)
        except ValueError:
            pass
    raise ValueError(f"Cannot parse {val} as an integer")


def _parse_float(val: Any) -> float:
    """
    Parse a float value from various input types.
    
    Args:
        val: The value to parse
        
    Returns:
        float: The parsed float value
        
    Raises:
        ValueError: If the value cannot be parsed as a float
    """
    if isinstance(val, float):
        return val
    if isinstance(val, int) and not isinstance(val, bool):
        return float(val)
    if isinstance(val, str):
        try:
            return float(val)
        except ValueError:
            pass
    raise ValueError(f"Cannot parse {val} as a float")


def _parse_list(val: Any, separator: str = ",") -> List[str]:
    """
    Parse a list value from various input types.
    
    Args:
        val: The value to parse
        separator: The separator to use for splitting strings
        
    Returns:
        List[str]: The parsed list value
        
    Raises:
        ValueError: If the value cannot be parsed as a list
    """
    if isinstance(val, list):
        return val
    if isinstance(val, str):
        return [item.strip() for item in val.split(separator) if item.strip()]
    raise ValueError(f"Cannot parse {val} as a list")


def _parse_enum(val: Any, enum_class: Type[Enum]) -> Enum:
    """
    Parse an enum value from various input types.
    
    Args:
        val: The value to parse
        enum_class: The enum class to use for parsing
        
    Returns:
        Enum: The parsed enum value
        
    Raises:
        ValueError: If the value cannot be parsed as an enum
    """
    if isinstance(val, enum_class):
        return val
    if isinstance(val, str):
        try:
            return enum_class(val)
        except ValueError:
            pass
    raise ValueError(f"Cannot parse {val} as {enum_class.__name__}")


def _get_env_value(
    env_var: str,
    default: Any = None,
    var_type: Type[T] = str,
    required: bool = False,
) -> T:
    """
    Get an environment variable with type conversion and validation.
    
    Args:
        env_var: The name of the environment variable
        default: The default value to use if the variable is not set
        var_type: The type to convert the value to
        required: Whether the variable is required
        
    Returns:
        The environment variable value converted to the specified type
        
    Raises:
        ConfigurationError: If the variable is required but not set
        ValueError: If the value cannot be converted to the specified type
    """
    value = os.getenv(env_var)
    
    if value is None:
        if required:
            raise ConfigurationError(f"Required environment variable {env_var} is not set")
        return default
    
    try:
        if var_type == bool:
            return cast(T, _parse_bool(value))
        elif var_type == int:
            return cast(T, _parse_int(value))
        elif var_type == float:
            return cast(T, _parse_float(value))
        elif var_type == List[str]:
            return cast(T, _parse_list(value))
        elif issubclass(var_type, Enum):
            return cast(T, _parse_enum(value, var_type))
        else:
            return cast(T, value)
    except ValueError as e:
        logger.warning(f"Error parsing {env_var}: {e}. Using default value: {default}")
        return default


def _get_required_env_value(env_var: str, description: str) -> str:
    """
    Get a required environment variable with proper validation.

    Args:
        env_var: The environment variable name
        description: Human-readable description for error messages

    Returns:
        The environment variable value

    Raises:
        ConfigurationError: If the environment variable is not set or invalid
    """
    value = os.getenv(env_var)

    if not value:
        app_env = os.getenv("APP_ENV", "development")
        raise ConfigurationError(
            f"{env_var} environment variable is required for {app_env} environment. "
            f"Please set this variable with a secure {description}."
        )

    # For JWT secrets, validate minimum length
    if "JWT_SECRET" in env_var and len(value) < 32:
        raise ConfigurationError(
            f"{env_var} must be at least 32 characters long for security. "
            f"Please generate a longer, more secure {description}."
        )

    return value


class BaseSettings:
    """Base class for settings with validation and reporting."""
    
    def __init__(self):
        """Initialize settings with validation."""
        self._validate()
    
    def _validate(self) -> None:
        """
        Validate settings.
        
        Raises:
            ValidationError: If validation fails
        """
        errors = self.validate()
        if errors:
            for error in errors:
                logger.error(f"Configuration validation error: {error}")
            raise ValidationError(f"Configuration validation failed: {errors}")
    
    def validate(self) -> List[str]:
        """
        Validate settings.
        
        Returns:
            List[str]: List of validation errors
        """
        return []
    
    def get_config_report(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """
        Generate a configuration report.
        
        Args:
            include_sensitive: Whether to include sensitive values
            
        Returns:
            Dict[str, Any]: Configuration report
        """
        report = {}
        for key, value in self.__dict__.items():
            if key.startswith("_"):
                continue
            
            # Skip sensitive values unless explicitly requested
            if not include_sensitive and self._is_sensitive(key):
                report[key] = self._mask_value(str(value))
            else:
                report[key] = value
        
        return report
    
    def _is_sensitive(self, key: str) -> bool:
        """
        Check if a key is sensitive.
        
        Args:
            key: The key to check
            
        Returns:
            bool: True if the key is sensitive, False otherwise
        """
        sensitive_patterns = [
            r".*_key$",
            r".*_secret$",
            r".*password.*",
            r".*token.*",
            r".*auth.*",
            r".*jwt.*",
        ]
        
        return any(re.match(pattern, key.lower()) for pattern in sensitive_patterns)
    
    def _mask_value(self, value: str) -> str:
        """
        Mask a sensitive value.
        
        Args:
            value: The value to mask
            
        Returns:
            str: The masked value
        """
        if not value or len(value) < 8:
            return "***" if value else None
        
        return f"{value[:4]}...{value[-4:]}"


# Export the get_config function for compatibility
def get_config() -> Dict[str, Any]:
    """
    Get configuration settings as a dictionary.

    This function provides compatibility for code that expects a get_config function.

    Returns:
        Dict[str, Any]: Configuration settings dictionary
    """
    from .settings import settings

    return settings.get_config_report(include_sensitive=False)


# Export commonly used items
__all__ = [
    "get_config",
    "BaseSettings",
    "LogLevel",
    "JWTAlgorithm",
    "ValidationError",
    "ConfigurationError",
]
