"""
Simple HTTP Server for PI Lawyer AI

This is a completely isolated minimal server using Python's built-in HTTP server
to avoid any dependency issues.
"""

import os
import sys
import logging
from datetime import datetime, timezone
import time
import platform
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("simple_server")

logger.info("Starting simple HTTP server...")

# Store server start time
START_TIME = time.time()


class HealthHandler(BaseHTTPRequestHandler):
    """Simple HTTP request handler for health checks"""
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # Set common headers
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        # Prepare response data
        timestamp = datetime.now(timezone.utc).isoformat()
        uptime_seconds = int(time.time() - START_TIME)
        
        if path == "/" or path == "/health":
            response_data = {
                "status": "ok",
                "timestamp": timestamp,
                "service": "pi-lawyer-ai-simple",
                "version": "1.0.0",
                "uptime_seconds": uptime_seconds,
                "python_version": platform.python_version(),
                "platform": platform.platform(),
                "environment": os.environ.get("APP_ENV", "production"),
                "message": "PI Lawyer AI - Simple Server Running"
            }
        elif path == "/ping":
            response_data = {
                "message": "pong",
                "timestamp": timestamp,
                "uptime_seconds": uptime_seconds
            }
        else:
            response_data = {
                "error": "Not Found",
                "path": path,
                "timestamp": timestamp,
                "available_endpoints": ["/", "/health", "/ping"]
            }
        
        # Send JSON response
        response_json = json.dumps(response_data, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
        
        # Log the request
        logger.info(f"Served {path} - Status: 200")
    
    def do_POST(self):
        """Handle POST requests"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # Set common headers
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        timestamp = datetime.now(timezone.utc).isoformat()
        
        if path == "/copilotkit":
            response_data = {
                "status": "minimal_mode",
                "message": "Running in minimal mode - full agent functionality not available",
                "timestamp": timestamp
            }
        else:
            response_data = {
                "error": "Not Found",
                "path": path,
                "timestamp": timestamp,
                "available_endpoints": ["/copilotkit"]
            }
        
        # Send JSON response
        response_json = json.dumps(response_data, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
        
        # Log the request
        logger.info(f"Served POST {path} - Status: 200")
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        logger.info(f"Served OPTIONS {self.path} - Status: 200")
    
    def log_message(self, format, *args):
        """Override to use our logger"""
        logger.info(f"{self.address_string()} - {format % args}")


def run_server():
    """Run the HTTP server"""
    # Get port from environment variable or default to 8000
    port = int(os.getenv("PORT", 8000))
    
    server_address = ('0.0.0.0', port)
    httpd = HTTPServer(server_address, HealthHandler)
    
    logger.info(f"Starting HTTP server on http://0.0.0.0:{port}")
    logger.info("Available endpoints: /, /health, /ping, /copilotkit")
    logger.info("Server is ready to handle requests")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("Server interrupted by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        raise
    finally:
        httpd.server_close()
        logger.info("Server stopped")


if __name__ == "__main__":
    run_server()
