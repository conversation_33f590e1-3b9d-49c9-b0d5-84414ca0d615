#!/bin/bash
# Startup script for LangGraph FastAPI application

# Don't exit on error, we want to handle errors gracefully
set +e

# Print Python and environment information
echo "Starting LangGraph FastAPI application"
echo "Python version: $(python --version)"
echo "Environment: $APP_ENV"

# Check for required environment variables
required_vars=(
  "OPENAI_API_KEY"
  "PINECONE_API_KEY"
  "PINECONE_ENVIRONMENT"
  "PINECONE_INDEX_NAME"
  "SUPABASE_URL"
  "SUPABASE_KEY"
  "CPK_ENDPOINT_SECRET"
)

missing_vars=()
for var in "${required_vars[@]}"; do
  if [ -z "${!var}" ]; then
    missing_vars+=("$var")
  fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
  echo "WARNING: Missing required environment variables:"
  for var in "${missing_vars[@]}"; do
    echo "  - $var"
  done
  echo "Please set these variables in the Fly.io dashboard or environment."
  echo "Setting up mock environment for testing purposes."

  # Set up mock environment variables for testing
  if [[ " ${missing_vars[@]} " =~ " OPENAI_API_KEY " ]]; then
    export OPENAI_API_KEY="sk-mock-key-for-testing"
    echo "Using mock OPENAI_API_KEY"
  fi

  if [[ " ${missing_vars[@]} " =~ " PINECONE_API_KEY " ]]; then
    export PINECONE_API_KEY="mock-pinecone-key-for-testing"
    echo "Using mock PINECONE_API_KEY"
  fi

  if [[ " ${missing_vars[@]} " =~ " PINECONE_ENVIRONMENT " ]]; then
    export PINECONE_ENVIRONMENT="us-east-1"
    echo "Using mock PINECONE_ENVIRONMENT"
  fi

  if [[ " ${missing_vars[@]} " =~ " PINECONE_INDEX_NAME " ]]; then
    export PINECONE_INDEX_NAME="mock-index"
    echo "Using mock PINECONE_INDEX_NAME"
  fi

  if [[ " ${missing_vars[@]} " =~ " SUPABASE_URL " ]]; then
    export SUPABASE_URL="https://mock-supabase-url.supabase.co"
    echo "Using mock SUPABASE_URL"
  fi

  if [[ " ${missing_vars[@]} " =~ " SUPABASE_KEY " ]]; then
    export SUPABASE_KEY="mock-supabase-key"
    echo "Using mock SUPABASE_KEY"
  fi

  if [[ " ${missing_vars[@]} " =~ " CPK_ENDPOINT_SECRET " ]]; then
    export CPK_ENDPOINT_SECRET="mock-endpoint-secret"
    echo "Using mock CPK_ENDPOINT_SECRET"
  fi

  if [[ " ${missing_vars[@]} " =~ " VOYAGE_API_KEY " ]]; then
    export VOYAGE_API_KEY="mock-voyage-key"
    echo "Using mock VOYAGE_API_KEY"
  fi

  echo "Mock environment variables set up for testing."
  echo "NOTE: Some functionality will be limited or unavailable."
fi

# Create log directory if it doesn't exist
mkdir -p /app/logs

# Run database migrations if needed
if [ -f "alembic.ini" ]; then
  echo "Running database migrations..."
  python -m alembic upgrade head
else
  echo "No alembic.ini found, skipping migrations."
fi

# Create a wrapper script to handle errors gracefully
cat > /tmp/run_app.py << EOF
import os
import sys
import uvicorn
import importlib.util
import traceback

def run_app():
    try:
        print("Starting basic FastAPI server...")

        # Change to the app directory
        os.chdir("/app")

        # Import FastAPI directly in the function to avoid import issues
        from fastapi import FastAPI
        from fastapi.middleware.cors import CORSMiddleware
        import uvicorn
        from datetime import datetime, timezone
        import time
        import platform

        print("FastAPI imported successfully")

        # Create a minimal FastAPI app
        app = FastAPI(
            title="PI Lawyer AI API",
            description="Basic server for PI Lawyer AI",
            version="1.0.0"
        )

        start_time = time.time()

        # Add basic CORS middleware
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        # Health check endpoint
        @app.get("/health")
        async def health_check():
            uptime_seconds = int(time.time() - start_time)
            system_info = {
                "python_version": platform.python_version(),
                "system": platform.system(),
                "platform": platform.platform(),
            }
            return {
                "status": "ok",
                "version": "1.0.0",
                "environment": os.environ.get("APP_ENV", "development"),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "uptime_seconds": uptime_seconds,
                "system_info": system_info,
            }

        # Root endpoint
        @app.get("/")
        async def root():
            return {"message": "PI Lawyer AI API is running (basic mode)"}

        # CopilotKit endpoint
        @app.post("/copilotkit")
        async def copilotkit():
            return {
                "status": "basic_mode",
                "message": "Running in basic mode - full agent functionality not available"
            }

        # Run the server
        port = int(os.environ.get("PORT", "8000"))
        print(f"Starting uvicorn server on port {port}...")
        uvicorn.run(app, host="0.0.0.0", port=port)

    except Exception as e:
        print(f"Error starting basic server: {e}")
        print("Traceback:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    run_app()
EOF

# Start the application with the wrapper script
echo "Starting FastAPI server..."
python /tmp/run_app.py
