"""
Jobs API Routes

This module provides REST API endpoints for job management and background task triggering.
"""

import logging
from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel, Field

from backend.api.dependencies.auth import get_current_user, get_current_tenant
from backend.api.dependencies.authorization import require_permission, Permission

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/jobs", tags=["jobs"])


# Pydantic models for request/response
class UserReturnInsightsRequest(BaseModel):
    """Request model for triggering user return insights."""
    user_id: str = Field(description="User ID")
    tenant_id: str = Field(description="Tenant ID")
    last_activity_time: str = Field(description="ISO timestamp of last activity")


class JobStatusResponse(BaseModel):
    """Response model for job status."""
    job_id: str
    status: str
    created_at: str
    completed_at: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@router.post("/user-return-insights")
async def trigger_user_return_insights(
    request: UserReturnInsightsRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
    current_tenant: Dict[str, Any] = Depends(get_current_tenant)
):
    """
    Trigger user return insights generation.
    
    This endpoint triggers the generation of personalized insights when a user
    returns to the application after being away for 1+ hours.
    """
    try:
        logger.info(f"Triggering user return insights for user {request.user_id}")
        
        # Validate that the requesting user matches or has permission
        if (current_user["id"] != request.user_id and 
            current_tenant["id"] != request.tenant_id and
            not current_user.get("is_super_admin", False)):
            raise HTTPException(status_code=403, detail="Insufficient permissions")
        
        # Submit background job for user return insights
        from jobs.helpers import trigger_user_return_insights
        job_id = trigger_user_return_insights(
            request.user_id, 
            request.tenant_id, 
            request.last_activity_time
        )
        
        if job_id:
            logger.info(f"User return insights job triggered: {job_id}")
            return JSONResponse(
                content={
                    "success": True,
                    "message": "User return insights generation triggered",
                    "job_id": job_id
                },
                status_code=202
            )
        else:
            return JSONResponse(
                content={
                    "success": False,
                    "message": "User return insights not triggered (insufficient time away)",
                    "job_id": None
                },
                status_code=200
            )
        
    except Exception as e:
        logger.error(f"Error triggering user return insights: {e}")
        raise HTTPException(status_code=500, detail="Failed to trigger user return insights")


@router.post("/deadline-insights")
async def trigger_deadline_insights(
    background_tasks: BackgroundTasks,
    tenant_ids: Optional[list] = None,
    current_user: Dict[str, Any] = Depends(get_current_user),
    current_tenant: Dict[str, Any] = Depends(get_current_tenant),
    _: None = Depends(require_permission(Permission.WRITE_DEADLINE_INSIGHTS))
):
    """
    Trigger deadline insights generation for specified tenants.
    
    This endpoint allows manual triggering of deadline insights analysis.
    """
    try:
        # If no tenant_ids specified, use current tenant
        if not tenant_ids:
            tenant_ids = [current_tenant["id"]]
        
        # Validate permissions for all requested tenants
        if not current_user.get("is_super_admin", False):
            # Non-super-admin users can only trigger for their own tenant
            if len(tenant_ids) > 1 or tenant_ids[0] != current_tenant["id"]:
                raise HTTPException(status_code=403, detail="Insufficient permissions")
        
        logger.info(f"Triggering deadline insights for tenants: {tenant_ids}")
        
        # Submit background job for deadline insights
        from jobs.helpers import submit_deadline_insights_job
        job_id = submit_deadline_insights_job(tenant_ids)
        
        logger.info(f"Deadline insights job triggered: {job_id}")
        
        return JSONResponse(
            content={
                "success": True,
                "message": "Deadline insights generation triggered",
                "job_id": job_id,
                "tenant_ids": tenant_ids
            },
            status_code=202
        )
        
    except Exception as e:
        logger.error(f"Error triggering deadline insights: {e}")
        raise HTTPException(status_code=500, detail="Failed to trigger deadline insights")


@router.get("/status/{job_id}", response_model=JobStatusResponse)
async def get_job_status(
    job_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get the status of a background job.
    
    Returns the current status, result, or error information for a job.
    """
    try:
        logger.info(f"Checking status for job {job_id}")
        
        # Check job status using Celery
        from jobs.helpers import check_job_status
        status_info = check_job_status(job_id)
        
        response = JobStatusResponse(
            job_id=job_id,
            status=status_info.get("status", "unknown"),
            created_at=status_info.get("checked_at", ""),
            completed_at=status_info.get("completed_at"),
            result=status_info.get("result"),
            error=status_info.get("error")
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error checking job status: {e}")
        raise HTTPException(status_code=500, detail="Failed to check job status")


@router.post("/deadline-alert")
async def trigger_deadline_alert(
    tenant_id: str,
    deadline_data: Dict[str, Any],
    background_tasks: BackgroundTasks,
    alert_type: str = "critical_deadline",
    current_user: Dict[str, Any] = Depends(get_current_user),
    current_tenant: Dict[str, Any] = Depends(get_current_tenant),
    _: None = Depends(require_permission(Permission.MANAGE_NOTIFICATIONS))
):
    """
    Trigger a deadline alert notification.
    
    This endpoint sends immediate deadline alerts to relevant users.
    """
    try:
        # Validate tenant access
        if (current_tenant["id"] != tenant_id and 
            not current_user.get("is_super_admin", False)):
            raise HTTPException(status_code=403, detail="Insufficient permissions")
        
        logger.info(f"Triggering deadline alert for tenant {tenant_id}, type: {alert_type}")
        
        # Submit background job for deadline alert
        from jobs.helpers import submit_deadline_alert_job
        job_id = submit_deadline_alert_job(tenant_id, deadline_data, alert_type)
        
        logger.info(f"Deadline alert job triggered: {job_id}")
        
        return JSONResponse(
            content={
                "success": True,
                "message": "Deadline alert triggered",
                "job_id": job_id,
                "alert_type": alert_type
            },
            status_code=202
        )
        
    except Exception as e:
        logger.error(f"Error triggering deadline alert: {e}")
        raise HTTPException(status_code=500, detail="Failed to trigger deadline alert")


@router.get("/active")
async def get_active_jobs(
    current_user: Dict[str, Any] = Depends(get_current_user),
    current_tenant: Dict[str, Any] = Depends(get_current_tenant)
):
    """
    Get list of active jobs for the current tenant.
    
    Returns information about currently running background jobs.
    """
    try:
        # This would require implementing job tracking in the database
        # For now, return a placeholder response
        
        logger.info(f"Fetching active jobs for tenant {current_tenant['id']}")
        
        # In a full implementation, this would query a jobs table
        # that tracks job status, tenant association, etc.
        
        return JSONResponse(
            content={
                "active_jobs": [],
                "total_count": 0,
                "message": "Job tracking not fully implemented yet"
            }
        )
        
    except Exception as e:
        logger.error(f"Error fetching active jobs: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch active jobs")
